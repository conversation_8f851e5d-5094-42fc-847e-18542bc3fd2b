{"name": "@woocommerce/blueprint", "description": "WooCommerce Blueprint package", "scripts": {"changelog": "XDEBUG_MODE=off composer install --quiet && composer exec -- changelogger", "lint": "pnpm --if-present '/^lint:lang:.*$/'", "lint:fix": "pnpm --if-present '/^lint:fix:lang:.*$/'", "lint:fix:lang:php": "composer run-script phpcbf", "lint:lang:php": "composer run-script phpcs", "postinstall": "XDEBUG_MODE=off composer install --quiet", "env:test": "pnpm env:dev", "env:dev": "pnpm wp-env start --update", "test:unit": "composer run-script test:unit", "test:php:ci": "pnpm test:unit"}, "license": "GPL-2.0-or-later", "devDependencies": {"@wordpress/env": "10.17.0"}, "config": {"ci": {"lint": {"command": "lint", "changes": ["src/**/*.php"]}, "tests": [{"name": "PHP: 8.1 WP: latest", "testType": "unit:php", "command": "test:php:ci", "changes": ["composer.json", "composer.lock", "**/*.php", "phpunit.xml", ".wp-env.json"], "testEnv": {"start": "env:test", "config": {"phpVersion": "8.1", "wpVersion": "latest"}}, "events": ["pull_request", "push"]}]}}}