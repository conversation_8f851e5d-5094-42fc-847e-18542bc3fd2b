{"name": "woocommerce/blueprint", "version": "0.0.1", "type": "wordpress-plugin", "autoload": {"psr-4": {"Automattic\\WooCommerce\\Blueprint\\": "src/"}}, "autoload-dev": {"psr-4": {"Automattic\\WooCommerce\\Blueprint\\Tests\\": "tests/"}}, "require": {"opis/json-schema": "^2.3"}, "scripts": {"test:setup": "wp-env start", "test:unit": "wp-env run tests-cli --env-cwd=wp-content/plugins/blueprint ./vendor/bin/phpunit", "phpcs": ["phpcs -s -p"], "phpcbf": ["phpcbf -p"]}, "require-dev": {"phpunit/phpunit": "^9", "mockery/mockery": "^1.6", "automattic/jetpack-changelogger": "3.3.0", "woocommerce/woocommerce-sniffs": "^1.0.0", "yoast/phpunit-polyfills": "^2.0"}, "extra": {"changelogger": {"formatter": {"filename": "../../../tools/changelogger/class-legacy-core-formatter.php"}, "types": {"fix": "Fixes an existing bug", "add": "Adds functionality", "update": "Update existing functionality", "dev": "Development related task", "tweak": "A minor adjustment to the codebase", "performance": "Address performance issues", "enhancement": "Improve existing functionality"}, "changelog": "changelog.md"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}}