/**
CSS reset for email clients for elements used in email content
StyleLint is disabled because some rules contain properties that linter marks as unknown (e.g. mso- prefix), but they are valid for email rendering
 */
/* stylelint-disable property-no-unknown */
table,
td {
	border-collapse: collapse;
	mso-table-lspace: 0;
	mso-table-rspace: 0;
}

img {
	border: 0;
	height: auto;
	-ms-interpolation-mode: bicubic;
	line-height: 100%;
	max-width: 100%;
	outline: none;
	text-decoration: none;
}

p {
	display: block;
	margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	margin-bottom: 0;
	margin-top: 0;
}

/* Ensure border style is set when a block has a border */
.has-border-color {
	border-style: solid;
}

/* We want ensure the same design for all email clients */
ul,
ol {
	/* When margin attribute is set to zero, Outlook doesn't render the list properly. As a possible workaround, we can reset only margin for top and bottom */
	margin-bottom: 0;
	margin-top: 0;
	padding: 0 0 0 40px;
}
/* Outlook was adding weird spaces around lists in some versions. Resetting vertical margin for list items solved it */
li {
	margin-bottom: 0;
	margin-top: 0;
}
