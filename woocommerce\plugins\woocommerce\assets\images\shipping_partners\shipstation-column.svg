<svg width="147" height="116" viewBox="0 0 147 116" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<rect x="32" y="0.5" width="84" height="115" rx="4.59053" fill="#9BDC79" fill-opacity="0.22"/>
<g filter="url(#filter0_d_4001_340659)">
<rect width="81.7778" height="92.4444" rx="4.30362" transform="matrix(-1 0 0 1 91.7773 11.3438)" fill="white"/>
</g>
<rect x="18" y="23" width="66" height="12" fill="url(#pattern0)"/>
<rect x="20" y="68.5" width="3" height="11" fill="#9BDC79"/>
<rect x="24.5" y="68.5" width="1" height="11" fill="#9BDC79"/>
<rect x="27" y="68.5" width="5" height="11" fill="#9BDC79"/>
<rect x="33.5" y="68.5" width="3" height="11" fill="#9BDC79"/>
<rect x="38" y="68.5" width="3" height="11" fill="#9BDC79"/>
<rect x="42.5" y="68.5" width="4" height="11" fill="#9BDC79"/>
<rect x="48" y="68.5" width="3" height="11" fill="#9BDC79"/>
<rect x="52.5" y="68.5" width="3" height="11" fill="#9BDC79"/>
<rect x="57" y="68.5" width="5" height="11" fill="#9BDC79"/>
<rect x="63.5" y="68.5" width="2" height="11" fill="#9BDC79"/>
<rect x="67" y="68.5" width="3" height="11" fill="#9BDC79"/>
<rect x="71.5" y="68.5" width="2" height="11" fill="#9BDC79"/>
<rect x="75" y="68.5" width="4" height="11" fill="#9BDC79"/>
<rect x="80.5" y="68.5" width="1" height="11" fill="#9BDC79"/>
<rect x="20" y="84.5" width="62" height="11" rx="1.72145" fill="#43921A"/>
<rect x="20.3105" y="47.5" width="25.6" height="4.26667" rx="2.13333" fill="#DCDCDE"/>
<rect x="20.3105" y="54.3203" width="61.1556" height="4.26666" rx="2.13333" fill="#DCDCDE"/>
<g filter="url(#filter1_d_4001_340659)">
<rect x="56.2217" y="40.8555" width="81.4222" height="28.4444" rx="4.30362" fill="white" shape-rendering="crispEdges"/>
<rect x="80.6084" y="46.5938" width="6.59889" height="2.29526" rx="1.14763" fill="#DCDCDE"/>
<rect x="80.6084" y="51.1836" width="33.2813" height="2.29526" rx="1.14763" fill="#DCDCDE"/>
<rect x="80.6084" y="55.7754" width="22.0919" height="2.29526" rx="1.14763" fill="#43921A"/>
<circle cx="69" cy="54.5" r="9" fill="#77C64E"/>
<mask id="mask0_4001_340659" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="65" y="50" width="7" height="9">
<path fill-rule="evenodd" clip-rule="evenodd" d="M65.292 53.9048L67.4003 56.0132L71.7087 51.7507L70.792 50.834L67.4003 54.2257L66.2087 53.034L65.292 53.9048ZM71.7087 57.2507H65.292V58.1673H71.7087V57.2507Z" fill="white"/>
</mask>
<g mask="url(#mask0_4001_340659)">
<rect x="63" y="49" width="11" height="11" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_4001_340659" x="0.818942" y="2.16269" width="100.139" height="110.807" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4.59053"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4001_340659"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4001_340659" result="shape"/>
</filter>
<pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_4001_340659" transform="matrix(0.00344828 0 0 0.0185185 -0.124138 -0.592593)"/>
</pattern>
<filter id="filter1_d_4001_340659" x="47.0406" y="34.5435" width="99.7845" height="46.8074" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.86908"/>
<feGaussianBlur stdDeviation="4.59053"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4001_340659"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4001_340659" result="shape"/>
</filter>
<image id="image0_4001_340659" width="372" height="119" xlink:href="data:image/png;base64,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"/>
</defs>
</svg>
